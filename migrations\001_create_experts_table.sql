-- Create Experts table
CREATE TABLE Experts (
    ExpertID INT IDENTITY(1,1) PRIMARY KEY,
    UserID INT NOT NULL,
    Specialty NVARCHAR(50),
    ShortBio NVARCHAR(200),
    Biography NVARCHAR(2000),
    Education NVARCHAR(1000),
    Certificates NVARCHAR(1000),
    ExperienceYears INT,
    HourlyRate DECIMAL(10, 2),
    IsVerified BIT DEFAULT 0,
    Rating DECIMAL(3, 2) DEFAULT 0,
    ReviewCount INT DEFAULT 0,
    CreatedAt DATETIME DEFAULT GETDATE(),
    UpdatedAt DATETIME DEFAULT GETDATE(),
    CONSTRAINT FK_Experts_Users FOREIGN KEY (UserID) REFERENCES Users(UserID)
);

-- Create ExpertAvailability table
CREATE TABLE ExpertAvailability (
    AvailabilityID INT IDENTITY(1,1) PRIMARY KEY,
    ExpertID INT NOT NULL,
    DayOfWeek INT, -- 0 = Sunday, 1 = Monday, etc. (NULL if not recurring)
    StartTime TIME NOT NULL,
    EndTime TIME NOT NULL,
    IsRecurring BIT DEFAULT 0,
    SpecificDate DATE, -- NULL if recurring
    CreatedAt DATETIME DEFAULT GETDATE(),
    UpdatedAt DATETIME DEFAULT GETDATE(),
    CONSTRAINT FK_ExpertAvailability_Experts FOREIGN KEY (ExpertID) REFERENCES Experts(ExpertID)
);

-- Create index for expert lookup by user ID
CREATE INDEX IX_Experts_UserID ON Experts(UserID);
-- Create index for expert availability lookup
CREATE INDEX IX_ExpertAvailability_ExpertID ON ExpertAvailability(ExpertID);
-- Create index for recurring availability by day of week
CREATE INDEX IX_ExpertAvailability_DayOfWeek ON ExpertAvailability(DayOfWeek) WHERE IsRecurring = 1;
-- Create index for specific date availability
CREATE INDEX IX_ExpertAvailability_SpecificDate ON ExpertAvailability(SpecificDate) WHERE IsRecurring = 0; 