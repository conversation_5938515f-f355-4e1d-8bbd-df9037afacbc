-- Add role-related columns to Users table if they don't exist already
-- IsExpert ve IsClient sütunları için kontrol yapılıyor
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'IsExpert')
BEGIN
    ALTER TABLE Users ADD IsExpert BIT DEFAULT 0;
    PRINT 'IsExpert column added to Users table.';
END
ELSE
BEGIN
    PRINT 'IsExpert column already exists.';
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'IsClient')
BEGIN
    ALTER TABLE Users ADD IsClient BIT DEFAULT 0;
    PRINT 'IsClient column added to Users table.';
END
ELSE
BEGIN
    PRINT 'IsClient column already exists.';
END
GO

-- ProfileImage sütunu için kontrol yapılıyor
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'ProfileImage')
BEGIN
    ALTER TABLE Users ADD ProfileImage NVARCHAR(255);
    PRINT 'ProfileImage column added to Users table.';
END
ELSE
BEGIN
    PRINT 'ProfileImage column already exists.';
END
GO

-- IsPhoneVerified sütunu için kontrol yapılıyor, eğer zaten varsa atlanacak
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'IsPhoneVerified')
BEGIN
    ALTER TABLE Users ADD IsPhoneVerified BIT DEFAULT 0;
    PRINT 'IsPhoneVerified column added to Users table.';
END
ELSE
BEGIN
    PRINT 'IsPhoneVerified column already exists.';
END
GO

-- LastLoginAt sütunu için kontrol yapılıyor, LastLogin adında benzer bir sütun olabilir
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'LastLoginAt')
BEGIN
    -- LastLogin'in varlığını kontrol et
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'LastLogin')
    BEGIN
        -- Burada LastLogin'in hangi türde olduğunu ve LastLoginAt ile aynı amaca hizmet edip etmediğini
        -- kontrol edip karar verebiliriz, şimdilik sadece varsa eklemeyi atlıyoruz
        PRINT 'LastLogin column already exists, skipping LastLoginAt creation';
    END
    ELSE
    BEGIN
        ALTER TABLE Users ADD LastLoginAt DATETIME;
        PRINT 'LastLoginAt column added to Users table.';
    END
END
ELSE
BEGIN
    PRINT 'LastLoginAt column already exists.';
END
GO

-- Create indexes for faster role-based filtering, if IsExpert column exists
-- Index yaratma işlemlerini QUOTED_IDENTIFIER ON ayarını kullanarak çalıştırıyoruz
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'IsExpert')
BEGIN
    PRINT 'Setting QUOTED_IDENTIFIER ON for index creation.';
    
    -- QUOTED_IDENTIFIER ayarını ON yapıyoruz ve index oluşturuyoruz
    EXEC('
    SET QUOTED_IDENTIFIER ON;
    
    IF NOT EXISTS (SELECT * FROM sys.indexes 
                  WHERE name=''IX_Users_IsExpert'' AND object_id = OBJECT_ID(''Users''))
    BEGIN
        CREATE INDEX IX_Users_IsExpert ON Users(IsExpert) WHERE IsExpert = 1;
        PRINT ''IX_Users_IsExpert index created successfully.'';
    END
    ELSE
    BEGIN
        PRINT ''IX_Users_IsExpert index already exists.'';
    END
    ');
END
ELSE
BEGIN
    PRINT 'IsExpert column does not exist in Users table.';
END
GO

-- Create index on IsClient if the column exists
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'IsClient')
BEGIN
    PRINT 'Setting QUOTED_IDENTIFIER ON for index creation.';
    
    -- QUOTED_IDENTIFIER ayarını ON yapıyoruz ve index oluşturuyoruz
    EXEC('
    SET QUOTED_IDENTIFIER ON;
    
    IF NOT EXISTS (SELECT * FROM sys.indexes 
                  WHERE name=''IX_Users_IsClient'' AND object_id = OBJECT_ID(''Users''))
    BEGIN
        CREATE INDEX IX_Users_IsClient ON Users(IsClient) WHERE IsClient = 1;
        PRINT ''IX_Users_IsClient index created successfully.'';
    END
    ELSE
    BEGIN
        PRINT ''IX_Users_IsClient index already exists.'';
    END
    ');
END
ELSE
BEGIN
    PRINT 'IsClient column does not exist in Users table.';
END
GO 