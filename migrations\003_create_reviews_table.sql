-- Create ExpertReviews table if it doesn't exist
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ExpertReviews')
BEGIN
    CREATE TABLE ExpertReviews (
        ReviewID INT IDENTITY(1,1) PRIMARY KEY,
        ExpertID INT NOT NULL,
        ClientID INT NOT NULL,
        AppointmentID INT,
        Rating DECIMAL(3, 2) NOT NULL,
        Comment NVARCHAR(1000),
        IsAnonymous BIT DEFAULT 0,
        IsVerified BIT DEFAULT 0,
        IsPublished BIT DEFAULT 1,
        CreatedAt DATETIME DEFAULT GETDATE(),
        UpdatedAt DATETIME DEFAULT GETDATE(),
        CONSTRAINT FK_ExpertReviews_Experts FOREIGN KEY (ExpertID) REFERENCES Experts(ExpertID),
        CONSTRAINT FK_ExpertReviews_Clients FOREIGN KEY (ClientID) REFERENCES Clients(ClientID),
        CONSTRAINT FK_ExpertReviews_Appointments FOREIGN KEY (AppointmentID) REFERENCES Appointments(AppointmentID)
    );

    PRINT 'ExpertReviews table created successfully.';
END
ELSE
BEGIN
    PRINT 'ExpertReviews table already exists.';
END
GO

-- Create trigger only if ExpertReviews table exists and the trigger doesn't exist
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ExpertReviews')
    AND NOT EXISTS (SELECT * FROM sys.triggers WHERE name = 'UpdateExpertRating')
BEGIN
    EXEC('
    CREATE TRIGGER UpdateExpertRating
    ON ExpertReviews
    AFTER INSERT, UPDATE, DELETE
    AS
    BEGIN
        SET NOCOUNT ON;
        
        -- Collect all affected expert IDs
        DECLARE @ExpertIDs TABLE (ExpertID INT);
        
        -- Add ExpertIDs from inserted and deleted records
        INSERT INTO @ExpertIDs
        SELECT ExpertID FROM inserted
        UNION
        SELECT ExpertID FROM deleted;
        
        -- Update each affected expert
        UPDATE Experts
        SET 
            Rating = (
                SELECT AVG(Rating) 
                FROM ExpertReviews 
                WHERE ExpertReviews.ExpertID = Experts.ExpertID
                AND IsPublished = 1
            ),
            ReviewCount = (
                SELECT COUNT(*) 
                FROM ExpertReviews 
                WHERE ExpertReviews.ExpertID = Experts.ExpertID
                AND IsPublished = 1
            ),
            UpdatedAt = GETDATE()
        WHERE ExpertID IN (SELECT ExpertID FROM @ExpertIDs);
    END
    ');
    
    PRINT 'UpdateExpertRating trigger created successfully.';
END
ELSE
BEGIN
    PRINT 'UpdateExpertRating trigger already exists or ExpertReviews table does not exist.';
END
GO

-- Create indexes only if ExpertReviews table exists and indexes don't exist
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ExpertReviews')
BEGIN
    -- ExpertID index
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ExpertReviews_ExpertID' AND object_id = OBJECT_ID('ExpertReviews'))
    BEGIN
        CREATE INDEX IX_ExpertReviews_ExpertID ON ExpertReviews(ExpertID);
        PRINT 'IX_ExpertReviews_ExpertID index created successfully.';
    END
    ELSE
    BEGIN
        PRINT 'IX_ExpertReviews_ExpertID index already exists.';
    END

    -- ClientID index
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ExpertReviews_ClientID' AND object_id = OBJECT_ID('ExpertReviews'))
    BEGIN
        CREATE INDEX IX_ExpertReviews_ClientID ON ExpertReviews(ClientID);
        PRINT 'IX_ExpertReviews_ClientID index created successfully.';
    END
    ELSE
    BEGIN
        PRINT 'IX_ExpertReviews_ClientID index already exists.';
    END

    -- AppointmentID index
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ExpertReviews_AppointmentID' AND object_id = OBJECT_ID('ExpertReviews'))
    BEGIN
        CREATE INDEX IX_ExpertReviews_AppointmentID ON ExpertReviews(AppointmentID);
        PRINT 'IX_ExpertReviews_AppointmentID index created successfully.';
    END
    ELSE
    BEGIN
        PRINT 'IX_ExpertReviews_AppointmentID index already exists.';
    END

    -- Rating index
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ExpertReviews_Rating' AND object_id = OBJECT_ID('ExpertReviews'))
    BEGIN
        CREATE INDEX IX_ExpertReviews_Rating ON ExpertReviews(Rating);
        PRINT 'IX_ExpertReviews_Rating index created successfully.';
    END
    ELSE
    BEGIN
        PRINT 'IX_ExpertReviews_Rating index already exists.';
    END

    -- IsPublished index
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ExpertReviews_IsPublished' AND object_id = OBJECT_ID('ExpertReviews'))
    BEGIN
        CREATE INDEX IX_ExpertReviews_IsPublished ON ExpertReviews(IsPublished);
        PRINT 'IX_ExpertReviews_IsPublished index created successfully.';
    END
    ELSE
    BEGIN
        PRINT 'IX_ExpertReviews_IsPublished index already exists.';
    END
END
ELSE
BEGIN
    PRINT 'ExpertReviews table does not exist. Indexes not created.';
END
GO 