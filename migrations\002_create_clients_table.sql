-- Create Clients table
CREATE TABLE Clients (
    ClientID INT IDENTITY(1,1) PRIMARY KEY,
    UserID INT NOT NULL,
    PreferredLanguage NVARCHAR(30),
    PreferredCommunication NVARCHAR(20),
    SpecialRequirements NVARCHAR(500),
    CreatedAt DATETIME DEFAULT GETDATE(),
    UpdatedAt DATETIME DEFAULT GETDATE(),
    CONSTRAINT FK_Clients_Users FOREIGN KEY (UserID) REFERENCES Users(UserID)
);

-- Create Appointments table
CREATE TABLE Appointments (
    AppointmentID INT IDENTITY(1,1) PRIMARY KEY,
    ExpertID INT NOT NULL,
    ClientID INT NOT NULL,
    StartTime DATETIME NOT NULL,
    EndTime DATETIME NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Pending',
    Notes NVARCHAR(500),
    CancellationReason NVARCHAR(500),
    CreatedAt DATETIME DEFAULT GETDATE(),
    UpdatedAt DATETIME DEFAULT GETDATE(),
    CONSTRAINT FK_Appointments_Experts FOREIGN KEY (ExpertID) REFERENCES Experts(ExpertID),
    CONSTRAINT FK_Appointments_Clients FOREIGN KEY (ClientID) REFERENCES Clients(ClientID)
);

-- Create index for client lookup by user ID
CREATE INDEX IX_Clients_UserID ON Clients(UserID);
-- Create index for appointment lookup by expert
CREATE INDEX IX_Appointments_ExpertID ON Appointments(ExpertID);
-- Create index for appointment lookup by client
CREATE INDEX IX_Appointments_ClientID ON Appointments(ClientID);
-- Create index for appointment lookup by date
CREATE INDEX IX_Appointments_StartTime ON Appointments(StartTime);
-- Create index for appointment lookup by status
CREATE INDEX IX_Appointments_Status ON Appointments(Status); 